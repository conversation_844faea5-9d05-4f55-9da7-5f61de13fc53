import 'dart:async';
import 'dart:io';

import 'package:excel/excel.dart' hide Border;
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:uuid/uuid.dart';
import '../../../services/database/database_helper.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../models/cattle_isar.dart';
import '../services/cattle_handler.dart';
import '../../../constants/app_bar.dart';
import '../../../widgets/fab_styles.dart';
import 'cattle_detail_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../../../utils/message_utils.dart';

final logger = Logger();

class _Strings {
  static const title = 'Cattle Records';
  static const exportRecords = 'Export Records';
  static const selectAll = 'Select All';
  static const selected = 'selected';
  static const cancelSelection = 'Cancel Selection';
  static const deleteSelected = 'Delete Selected';
  static const selectToDelete = 'Select to Delete';
  static const pleaseSelectItems = 'Please select items to delete';
  static const addCattle = 'Add Cattle';
  static const searchCattle = 'Search cattle...';

  static const noName = 'No name';
  static const unknownType = 'Unknown type';
  static const unknownBreed = 'Unknown breed';
  static const unknown = 'Unknown';

  static const viewDetails = 'View Details';
  static const edit = 'Edit';
  static const delete = 'Delete';

  // Error messages
  static const exportError = 'Failed to export records';
  static const openFileError = 'Could not open file location';
}

class CattleRecordsScreen extends StatefulWidget {
  const CattleRecordsScreen({Key? key}) : super(key: key);

  @override
  State<CattleRecordsScreen> createState() => _CattleRecordsScreenState();
}

class _CattleRecordsScreenState extends State<CattleRecordsScreen> {
  final DatabaseHelper _db = DatabaseHelper.instance;
  late final CattleHandler _cattleHandler;
  late final FarmSetupHandler _farmSetupHandler;

  List<CattleIsar> _cattle = [];
  List<BreedCategoryIsar> _breeds = [];
  List<AnimalTypeIsar> _animalTypes = [];
  Map<String?, BreedCategoryIsar> _breedMap = {};
  Map<String?, AnimalTypeIsar> _animalTypeMap = {};
  final Set<String> _selectedCattle = {};
  bool _isSelectionMode = false;
  bool _isLoading = true;
  String _searchQuery = '';
  final _searchController = TextEditingController();
  String _selectedGender = 'All';
  String _selectedBreed = 'All';
  String _selectedAnimalType = 'All';

  String _sortBy = 'Name';
  bool _sortAscending = true;

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, dynamic>>? _cattleStreamSubscription;

  @override
  void initState() {
    super.initState();
    _cattleHandler = _db.cattleHandlerInstance;
    _farmSetupHandler = _db.farmSetupHandlerInstance;
    _searchController.addListener(() {
      if (_searchController.text != _searchQuery) {
        setState(() => _searchQuery = _searchController.text);
      }
    });
    _setupStreamListener();
    _loadData();
  }

  @override
  void dispose() {
    _cattleStreamSubscription?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  /// Set up stream listener for real-time cattle updates
  void _setupStreamListener() {
    _cattleStreamSubscription = _db.cattleRecordStream.listen((data) {
      debugPrint('🔄 Cattle stream update received: ${data['action']} for cattle ${data['cattleId']}');

      // Reload data when cattle are added, updated, or deleted
      if (data['action'] == 'add' || data['action'] == 'update' || data['action'] == 'delete') {
        _loadData();
      }
    }, onError: (error) {
      debugPrint('❌ Error in cattle stream: $error');
    });
  }

  List<CattleIsar> get filteredCattle {
    List<CattleIsar> result = List.from(_cattle);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      result = result
          .where((cattle) =>
              (cattle.name != null &&
                  cattle.name!
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase())) ||
              (cattle.tagId != null &&
                  cattle.tagId!
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase())))
          .toList();
    }

    // Apply gender filter
    if (_selectedGender != 'All') {
      result =
          result.where((cattle) => cattle.gender == _selectedGender).toList();
    }

    // Apply breed filter
    if (_selectedBreed != 'All') {
      result = result.where((cattle) {
        final displayData = _getResolvedCattleDisplayData(cattle);
        return displayData['breedName'] == _selectedBreed;
      }).toList();
    }

    // Apply animal type filter
    if (_selectedAnimalType != 'All') {
      result = result.where((cattle) {
        final displayData = _getResolvedCattleDisplayData(cattle);
        return displayData['animalTypeName'] == _selectedAnimalType;
      }).toList();
    }



    // Apply sorting - Fix the case mismatch between popup menu values and switch cases
    result.sort((a, b) {
      int comparison;
      switch (_sortBy.toLowerCase()) {
        // Convert to lowercase to match popup menu values
        case 'name':
          comparison = (a.name ?? '').compareTo(b.name ?? '');
          break;
        case 'tag':
        case 'tag id': // Handle both formats
          comparison = (a.tagId ?? '').compareTo(b.tagId ?? '');
          break;
        case 'dateofbirth':
        case 'date of birth': // Handle both formats
          final aDate = a.dateOfBirth ?? DateTime(1900);
          final bDate = b.dateOfBirth ?? DateTime(1900);
          comparison = aDate.compareTo(bDate);
          break;
        default:
          comparison = 0;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return result;
  }

  /// Identifies mismatched IDs and logs them without automatically updating the database
  Future<Map<String, List<String>>> _identifyDataIssues() async {
    debugPrint('\n\n======== IDENTIFYING DATA ISSUES ========');
    Map<String, List<String>> issuesMap = {
      'animalTypeIssues': [],
      'breedIssues': [],
      'specialCases': [],
    };

    // Check for cattle with invalid animal type IDs
    for (var cattle in _cattle) {
      final animalTypeId = cattle.animalTypeId;
      if (animalTypeId != null &&
          animalTypeId.isNotEmpty &&
          !_animalTypeMap.containsKey(animalTypeId)) {
        // Look for a similar ID with case-insensitive or trimmed matching
        bool potentialMatchFound = false;
        for (var type in _animalTypes) {
          if (type.businessId != null &&
              (type.businessId!.toLowerCase() == animalTypeId.toLowerCase() ||
                  type.businessId!.trim() == animalTypeId.trim())) {
            // Found a potential match - log it
            debugPrint(
                '🔍 Potential animal type ID match for ${cattle.name} (${cattle.tagId}): $animalTypeId -> ${type.businessId}');
            issuesMap['animalTypeIssues']!.add(
                '${cattle.name} (${cattle.tagId}): Invalid animal type ID might match ${type.name}');
            potentialMatchFound = true;
            break;
          }
        }

        if (!potentialMatchFound) {
          debugPrint(
              '❌ No matching animal type found for ${cattle.name} (${cattle.tagId}) with ID: $animalTypeId');
          issuesMap['animalTypeIssues']!.add(
              '${cattle.name} (${cattle.tagId}): Invalid animal type ID with no match');
        }

        // Special handling for "Mother" with tag "C1" - Fix both UI and database
        if (cattle.name == "Mother" &&
            cattle.tagId == "C1" &&
            (animalTypeId == "928e9a0f-1cac-4f0e-9dca-0f3ca5b1e889" ||
                !_animalTypeMap.containsKey(animalTypeId))) {
          debugPrint(
              '⚠️ Special case: "Mother" cattle (C1) with invalid animal type ID');
          issuesMap['specialCases']!.add(
              '"Mother" cattle has invalid animal type ID - Fixing in database');

          // First ensure we have the default animal types and breeds
          try {
            // Find or create the Cow animal type
            var cowType = _animalTypes.firstWhereOrNull(
                (type) => type.name?.toLowerCase() == "cow");

            if (cowType == null) {
              // Create default Cow type
              cowType = AnimalTypeIsar()
                ..businessId = const Uuid().v4()
                ..name = "Cow"
                ..iconCodePoint = Icons.pets.codePoint
                ..iconFontFamily = Icons.pets.fontFamily
                ..colorValue = 0xFF8B4513 // Hardcoded value for brown
                ..defaultGestationDays = 280
                ..defaultHeatCycleDays = 21
                ..defaultBreedingAge = 15
                ..createdAt = DateTime.now()
                ..updatedAt = DateTime.now();

              await _farmSetupHandler.addOrUpdateAnimalType(cowType);
              _animalTypes.add(cowType);
            }

            // Find or create default Holstein breed
            var holsteinBreed = _breeds.firstWhereOrNull(
                (breed) => breed.name?.toLowerCase() == "holstein" &&
                          breed.animalTypeId == cowType?.businessId);

            if (holsteinBreed == null) {
              // Create default Holstein breed
              holsteinBreed = BreedCategoryIsar()
                ..businessId = const Uuid().v4()
                ..name = "Holstein"
                ..animalTypeId = cowType.businessId
                ..description = "Black and white dairy breed known for high milk production"
                ..iconCodePoint = Icons.pets.codePoint
                ..iconFontFamily = Icons.pets.fontFamily
                ..colorValue = 0xFF2196F3 // Hardcoded value for blue
                ..createdAt = DateTime.now()
                ..updatedAt = DateTime.now();

              await _farmSetupHandler.addOrUpdateBreedCategory(holsteinBreed);
              _breeds.add(holsteinBreed);
            }

            // Now update the Mother cattle record
            cattle.animalTypeId = cowType.businessId;
            cattle.breedId = holsteinBreed.businessId;

            await _cattleHandler.updateCattle(cattle);

            // Update the UI mappings
            _animalTypeMap[cowType.businessId] = cowType;
            _breedMap[holsteinBreed.businessId] = holsteinBreed;

            debugPrint(
                '✅ Updated Mother (C1) animal type ID in database: ${cowType.businessId} -> ${cowType.name}');
            debugPrint(
                '✅ Updated Mother (C1) breed ID in database: ${holsteinBreed.businessId} -> ${holsteinBreed.name}');
          } catch (e) {
            debugPrint('❌ Error fixing Mother cattle record: $e');
            issuesMap['specialCases']!.add(
                'Error fixing Mother cattle: $e');
          }
        }
      }
    }

    // Check for cattle with invalid breed IDs
    for (var cattle in _cattle) {
      final breedId = cattle.breedId;
      if (breedId != null &&
          breedId.isNotEmpty &&
          !_breedMap.containsKey(breedId)) {
        // Look for a similar ID with case-insensitive or trimmed matching
        bool potentialMatchFound = false;
        for (var breed in _breeds) {
          if (breed.businessId != null &&
              (breed.businessId!.toLowerCase() == breedId.toLowerCase() ||
                  breed.businessId!.trim() == breedId.trim())) {
            // Found a potential match - log it
            debugPrint(
                '🔍 Potential breed ID match for ${cattle.name} (${cattle.tagId}): $breedId -> ${breed.businessId}');
            issuesMap['breedIssues']!.add(
                '${cattle.name} (${cattle.tagId}): Invalid breed ID might match ${breed.name}');
            potentialMatchFound = true;
            break;
          }
        }

        if (!potentialMatchFound) {
          debugPrint(
              '❌ No matching breed found for ${cattle.name} (${cattle.tagId}) with ID: $breedId');
          issuesMap['breedIssues']!.add(
              '${cattle.name} (${cattle.tagId}): Invalid breed ID with no match');
        }
      }
    }

    int totalIssues = issuesMap['animalTypeIssues']!.length +
        issuesMap['breedIssues']!.length;
    if (totalIssues > 0) {
      debugPrint('⚠️ Found $totalIssues data issues that need attention');
    } else {
      debugPrint('✅ No data issues found');
    }
    debugPrint('======== DATA ISSUE IDENTIFICATION COMPLETE ========\n\n');

    return issuesMap;
  }

  /// Shows a dialog with data issues and options to repair them
  void _showDataIssuesDialog(Map<String, List<String>> issues) {
    if (issues['animalTypeIssues']!.isNotEmpty ||
        issues['breedIssues']!.isNotEmpty) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Data Issues Detected'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Some cattle records have inconsistent data that may affect filtering and display.',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                if (issues['animalTypeIssues']!.isNotEmpty) ...[
                  Text(
                      'Animal Type Issues (${issues['animalTypeIssues']!.length}):',
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...issues['animalTypeIssues']!.take(5).map((issue) => Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text('• $issue'),
                      )),
                  if (issues['animalTypeIssues']!.length > 5)
                    Text(
                        '...and ${issues['animalTypeIssues']!.length - 5} more'),
                  const SizedBox(height: 16),
                ],
                if (issues['breedIssues']!.isNotEmpty) ...[
                  Text('Breed Issues (${issues['breedIssues']!.length}):',
                      style: const TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  ...issues['breedIssues']!.take(5).map((issue) => Padding(
                        padding: const EdgeInsets.only(bottom: 4.0),
                        child: Text('• $issue'),
                      )),
                  if (issues['breedIssues']!.length > 5)
                    Text('...and ${issues['breedIssues']!.length - 5} more'),
                  const SizedBox(height: 16),
                ],
                const Text(
                  'Note: These issues may affect filtering and reporting. Please use the Data Health tool to fix them.',
                  style: TextStyle(fontStyle: FontStyle.italic),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('DISMISS'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // In a real app, this would navigate to a data health/repair tool
                CattleMessageUtils.showInfo(context,
                    'Data Health Tool will be available in a future update');
              },
              child: const Text('VIEW DATA HEALTH TOOL'),
            ),
          ],
        ),
      );
    }
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load data concurrently with Future.wait for better performance
      final results = await Future.wait([
        _cattleHandler.getAllCattle(),
        _db.farmSetupHandler.getAllBreedCategories(),
        _db.farmSetupHandler.getAllAnimalTypes(),
      ]);

      final cattleList = results[0] as List<CattleIsar>;
      final breeds = results[1] as List<BreedCategoryIsar>;
      final animalTypes = results[2] as List<AnimalTypeIsar>;

      debugPrint('\n\n======== DATA LOADING DIAGNOSTICS ========');
      debugPrint('✅ Loaded ${cattleList.length} cattle records');
      debugPrint('✅ Loaded ${breeds.length} breed categories');
      debugPrint('✅ Loaded ${animalTypes.length} animal types');

      // Create maps for faster lookups - use null-safe approach for keys
      final breedMap = <String?, BreedCategoryIsar>{};
      for (var breed in breeds) {
        if (breed.businessId != null) {
          breedMap[breed.businessId] = breed;
          debugPrint(
              'Added breed to map: ID=${breed.businessId}, Name=${breed.name}');
        } else {
          debugPrint('⚠️ Breed has null businessId: ${breed.name}');
        }
      }

      final animalTypeMap = <String?, AnimalTypeIsar>{};
      for (var type in animalTypes) {
        if (type.businessId != null) {
          animalTypeMap[type.businessId] = type;
          debugPrint(
              'Added animal type to map: ID=${type.businessId}, Name=${type.name}');
        } else {
          debugPrint('⚠️ Animal type has null businessId: ${type.name}');
        }
      }

      // Enhanced debugging for animal type data issues
      debugPrint('\n======== ANIMAL TYPE VALIDATION ========');
      int invalidAnimalTypeCount = 0;
      for (var cattle in cattleList) {
        final animalTypeId = cattle.animalTypeId;

        // Check for potential issues with the animal type ID
        if (animalTypeId == null || animalTypeId.isEmpty) {
          debugPrint(
              '⚠️ ${cattle.name} (${cattle.tagId}): Missing animal type ID');
          invalidAnimalTypeCount++;
        } else if (!animalTypeMap.containsKey(animalTypeId)) {
          debugPrint(
              '⚠️ ${cattle.name} (${cattle.tagId}): Invalid animal type ID: $animalTypeId - not found in database');
          invalidAnimalTypeCount++;

          // Check for case sensitivity or whitespace issues
          final similarIds = animalTypes
              .where((type) =>
                  type.businessId != null &&
                  (type.businessId!.toLowerCase() ==
                          animalTypeId.toLowerCase() ||
                      type.businessId!.trim() == animalTypeId.trim()))
              .toList();

          if (similarIds.isNotEmpty) {
            debugPrint(
                '   💡 Possible match found: ${similarIds.first.businessId} (${similarIds.first.name})');
          }
        }
      }

      // Enhanced debugging for breed data issues
      debugPrint('\n======== BREED VALIDATION ========');
      int invalidBreedCount = 0;
      for (var cattle in cattleList) {
        final breedId = cattle.breedId;

        // Check for potential issues with the breed ID
        if (breedId == null || breedId.isEmpty) {
          debugPrint('⚠️ ${cattle.name} (${cattle.tagId}): Missing breed ID');
          invalidBreedCount++;
        } else if (!breedMap.containsKey(breedId)) {
          debugPrint(
              '⚠️ ${cattle.name} (${cattle.tagId}): Invalid breed ID: $breedId - not found in database');
          invalidBreedCount++;

          // Check for case sensitivity or whitespace issues
          final similarIds = breeds
              .where((breed) =>
                  breed.businessId != null &&
                  (breed.businessId!.toLowerCase() == breedId.toLowerCase() ||
                      breed.businessId!.trim() == breedId.trim()))
              .toList();

          if (similarIds.isNotEmpty) {
            debugPrint(
                '   💡 Possible match found: ${similarIds.first.businessId} (${similarIds.first.name})');
          }
        }
      }

      if (invalidAnimalTypeCount > 0) {
        debugPrint(
            '\n⚠️ Found $invalidAnimalTypeCount cattle with invalid animal type IDs');
      } else {
        debugPrint('\n✅ All cattle have valid animal type IDs');
      }

      if (invalidBreedCount > 0) {
        debugPrint(
            '\n⚠️ Found $invalidBreedCount cattle with invalid breed IDs');
      } else {
        debugPrint('\n✅ All cattle have valid breed IDs');
      }

      debugPrint('=============================================\n\n');

      // Update state with loaded data
      if (mounted) {
        setState(() {
          _cattle = cattleList;
          _breeds = breeds;
          _animalTypes = animalTypes;
          _breedMap = breedMap;
          _animalTypeMap = animalTypeMap;
          _isLoading = false;
        });

        // Attempt to repair any data issues detected
        if (invalidAnimalTypeCount > 0 || invalidBreedCount > 0) {
          _identifyDataIssues().then((issues) {
            if (mounted) {
              _showDataIssuesDialog(issues);
            }
          });
        }
      }
    } catch (e, stackTrace) {
      debugPrint('Error loading data: $e');
      debugPrint(stackTrace.toString());

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        CattleMessageUtils.showError(context,
            'Error loading data: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  void _showAddCattleDialog() {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by clicking outside
      builder: (dialogContext) => PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          // Updated callback from onPopInvoked to onPopInvokedWithResult
          // No need to return anything as PopScope handles this differently
        },
        child: CattleFormDialog(
          cattle:
              null, // Use null to indicate this is a new cattle, not an edit
          existingCattle: _cattle,
          businessId: const Uuid().v4(),
          animalTypes: _animalTypes,
          onSave: (newCattle) async {
            debugPrint('DEBUG: onSave callback triggered for new cattle');

            // Capture context before any async gaps.

            try {
              // Dialog will be popped by the CattleFormDialog itself
              // No need to pop it here to avoid double-popping

              if (!mounted) return;
              CattleMessageUtils.showInfo(context, 'Adding cattle...');

              await _cattleHandler.addCattle(newCattle);

              if (!mounted) return;

              // Use Future.microtask to prevent immediate navigation
              Future.microtask(() {
                if (mounted) {
                  setState(() {
                    _cattle.add(newCattle);
                    // Ensure UI is refreshed by reloading the data
                    _loadData();
                  });

                  CattleMessageUtils.showSuccess(context,
                      CattleMessageUtils.cattleRecordCreated());

                  debugPrint(
                      'DEBUG: Successfully added cattle and stayed on screen');
                }
              });
            } catch (e, s) {
              debugPrint('Error adding cattle: $e\n$s');

              if (!mounted) return; // Keep the primary mounted check
              // Use the captured context variables
              CattleMessageUtils.showError(context,
                  'Failed to add cattle: ${_getReadableErrorMessage(e)}');
            }
          },
        ),
      ),
    );
  }

  void _showEditCattleDialog(CattleIsar cattle) {
    showDialog(
      context: context,
      barrierDismissible: false, // Prevent dismissing by clicking outside
      builder: (dialogContext) => PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, result) {
          // Updated callback from onPopInvoked to onPopInvokedWithResult
          // No need to return anything as PopScope handles this differently
        },
        child: CattleFormDialog(
          cattle: cattle,
          animalTypes: _animalTypes,
          existingCattle: _cattle,
          businessId: cattle.businessId ?? '',
          onSave: (updatedCattle) async {
            debugPrint('DEBUG: onSave callback triggered for edit cattle');

            // Capture context before any async gaps.

            try {
              // Dialog will be popped by the CattleFormDialog itself
              // No need to pop it here to avoid double-popping

              if (!mounted) return;
              CattleMessageUtils.showInfo(context, 'Updating cattle...');

              await _cattleHandler.updateCattle(updatedCattle);

              if (!mounted) return;

              // Use Future.microtask to prevent immediate navigation
              Future.microtask(() {
                if (mounted) {
                  setState(() {
                    final index = _cattle.indexWhere(
                        (c) => c.businessId == updatedCattle.businessId);
                    if (index != -1) {
                      _cattle[index] = updatedCattle;
                    }
                  });

                  if (mounted) {
                    CattleMessageUtils.showSuccess(context, CattleMessageUtils.cattleRecordUpdated());
                  }

                  debugPrint(
                      'DEBUG: Successfully updated cattle and stayed on screen');
                }
              });
            } catch (e, s) {
              debugPrint('Error updating cattle: $e\n$s');

              if (!mounted) return; // Keep the primary mounted check
              // Use the captured context variables
              CattleMessageUtils.showError(context,
                  'Failed to update cattle: ${_getReadableErrorMessage(e)}');
            }
          },
        ),
      ),
    );
  }

  void _navigateToCattleDetail({
    required String businessId,
    required CattleIsar existingCattle,
    required Function(CattleIsar) onCattleUpdated,
  }) {
    debugPrint(
        'DEBUG: Navigating to cattle detail screen for businessId: $businessId');

    try {
      // Use push with a callback to handle the return value
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CattleDetailScreen(
            businessId: businessId,
            existingCattle: existingCattle,
            onCattleUpdated: (updatedCattle) {
              // Optimize by updating just the specific cattle item instead of reloading all
              if (!mounted) return;
              debugPrint(
                  'DEBUG: Cattle updated, refreshing UI with updated data');

              setState(() {
                final index = _cattle.indexWhere(
                    (c) => c.businessId == updatedCattle.businessId);
                if (index != -1) {
                  _cattle[index] = updatedCattle;
                  debugPrint('DEBUG: Updated cattle at index $index in list');
                } else {
                  debugPrint(
                      'WARNING: Could not find cattle with businessId ${updatedCattle.businessId} in list');
                  // Add it to the list if not found
                  _cattle.add(updatedCattle);
                  debugPrint('DEBUG: Added updated cattle to list');
                }
              });

              // Call the original callback if needed
              onCattleUpdated(updatedCattle);
            },
          ),
          maintainState: true, // Keep the state of the previous screen
        ),
      );
    } catch (e, s) {
      debugPrint('ERROR: Failed to navigate to cattle detail screen: $e');
      debugPrint('ERROR: Stack trace: $s');

      // Show error message
      CattleMessageUtils.showError(context, 'Error opening cattle details: ${_getReadableErrorMessage(e)}');
    }
  }

  Future<List<CattleIsar>> _getSelectedCattle() async {
    if (_selectedCattle.isEmpty) return [];

    List<CattleIsar> selected = [];

    // Option 1: Optimize by filtering from already loaded cattle if possible
    for (var cattleId in _selectedCattle) {
      final matchingCattle =
          _cattle.where((c) => c.businessId == cattleId).toList();
      if (matchingCattle.isNotEmpty) {
        selected.add(matchingCattle.first);
      }
    }

    // If we found all selected cattle in memory, return them
    if (selected.length == _selectedCattle.length) {
      return selected;
    }

    // Option 2: Fallback to individual fetches if needed
    selected = [];
    for (var cattleId in _selectedCattle) {
      try {
        final cattle = await _cattleHandler.getCattleById(cattleId);
        if (cattle != null) {
          selected.add(cattle);
        }
      } catch (e, s) {
        debugPrint('Error fetching cattle with ID $cattleId: $e\n$s');
      }
    }
    return selected;
  }

  Future<void> _exportRecords(String format) async {
    // Check if the widget is still mounted
    if (!mounted) return;

    // Show loading indicator
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                )),
            SizedBox(width: 16),
            Text('Exporting records...'),
          ],
        ),
        duration: Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(16),
      ),
    );

    try {
      String filePath = '';
      switch (format.toLowerCase()) {
        case 'csv':
          filePath = await _exportToCsv();
          break;
        case 'excel':
          filePath = await _exportToExcel();
          break;
        case 'pdf':
          filePath = await _exportToPdf();
          break;
      }

      // Check if the widget is still mounted before showing SnackBar
      if (!mounted) return;

      CattleMessageUtils.showSuccess(context, 'Records exported to ${filePath.split('\\').last}');

                if (platform == 'windows') {
                  await Process.run('explorer.exe', ['/select,', filePath]);
                } else if (platform == 'macos') {
                  await Process.run('open', ['-R', filePath]);
                } else if (platform == 'linux') {
                  await Process.run('xdg-open', [filePath]);
                } else {
                  // For other platforms, show a message
                  if (!mounted) return;
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('File saved to: $filePath'),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      behavior: SnackBarBehavior.floating,
                      margin: const EdgeInsets.all(16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                }
              } catch (e, s) {
                debugPrint('Error opening file location: $e\n$s');

                // Check if the widget is still mounted before showing SnackBar
                if (!mounted) return;

                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text(_Strings.openFileError),
                    backgroundColor: Theme.of(context).colorScheme.error,
                    behavior: SnackBarBehavior.floating,
                    margin: const EdgeInsets.all(16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                );
              }
            },
          ),
        ),
      );
    } catch (e, s) {
      debugPrint('Error exporting records: $e\n$s');

      // Check if the widget is still mounted before showing SnackBar
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content:
              Text('${_Strings.exportError}: ${_getReadableErrorMessage(e)}'),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  // Helper method to convert technical errors to user-friendly messages
  String _getReadableErrorMessage(dynamic error) {
    // Check for specific exception types from our database exceptions
    if (error is ValidationException) {
      return 'Validation error: ${error.message}';
    } else if (error is RecordNotFoundException) {
      return 'Record not found: ${error.message}';
    } else if (error is DatabaseException) {
      return 'Database error: ${error.message}';
    }

    // Otherwise use string analysis for other types of errors
    final errorStr = error.toString().toLowerCase();

    if (errorStr.contains('permission')) {
      return 'Permission denied. Please check app permissions.';
    } else if (errorStr.contains('not found') || errorStr.contains('no such')) {
      return 'File or directory not found.';
    } else if (errorStr.contains('disk') || errorStr.contains('storage')) {
      return 'Not enough storage space.';
    } else if (errorStr.contains('timeout') || errorStr.contains('timed out')) {
      return 'The operation timed out. Please try again.';
    } else if (errorStr.contains('network') ||
        errorStr.contains('connection')) {
      return 'Network error. Please check your connection.';
    } else if (errorStr.contains('duplicate') ||
        errorStr.contains('already exists')) {
      return 'A record with this information already exists.';
    } else if (errorStr.contains('invalid') ||
        errorStr.contains('invalid format')) {
      return 'Invalid data format.';
    }

    // Log the full error for debugging but return a simplified message
    debugPrint('Unhandled error type: ${error.runtimeType}, message: $error');
    return 'An unexpected error occurred. Please try again.';
  }

  Future<String> _exportToCsv() async {
    final directory = await getApplicationDocumentsDirectory();

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.csv';
    final csvData = StringBuffer();
    csvData.writeln('Name,Tag ID,Gender,Animal Type,Date of Birth,Breed');

    final recordsToExport =
        _selectedCattle.isEmpty ? filteredCattle : await _getSelectedCattle();

    for (var cattle in recordsToExport) {
      final displayData = _getResolvedCattleDisplayData(cattle);

      csvData.writeln(
          '${_escapeCsvField(displayData['name'])},${_escapeCsvField(displayData['tagId'])},${_escapeCsvField(displayData['gender'])},'
          '${_escapeCsvField(displayData['animalTypeName'])},${_escapeCsvField(displayData['dateOfBirth']?.toString() ?? '')},'
          '${_escapeCsvField(displayData['breedName'])}');
    }

    await File(filePath).writeAsString(csvData.toString());
    return filePath;
  }

  Future<String> _exportToExcel() async {
    final directory = await getApplicationDocumentsDirectory();

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.xlsx';
    final excel = Excel.createExcel();
    final sheet = excel['Cattle Records'];

    final headers = [
      'Name',
      'Tag ID',
      'Gender',
      'Animal Type',
      'Date of Birth',
      'Breed'
    ];
    for (var i = 0; i < headers.length; i++) {
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .value = TextCellValue(headers[i]);
      sheet
          .cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0))
          .cellStyle = CellStyle(
        bold: true,
        horizontalAlign: HorizontalAlign.Center,
      );
    }

    final recordsToExport =
        _selectedCattle.isEmpty ? filteredCattle : await _getSelectedCattle();

    for (var i = 0; i < recordsToExport.length; i++) {
      final cattle = recordsToExport[i];
      final displayData = _getResolvedCattleDisplayData(cattle);

      final rowData = [
        displayData['name'],
        displayData['tagId'],
        displayData['gender'],
        displayData['animalTypeName'],
        displayData['dateOfBirth']?.toString() ?? '',
        displayData['breedName'],
      ];

      for (var j = 0; j < rowData.length; j++) {
        sheet
            .cell(CellIndex.indexByColumnRow(columnIndex: j, rowIndex: i + 1))
            .value = TextCellValue(rowData[j]);
      }
    }

    final excelFile = File(filePath);
    await excelFile.writeAsBytes(excel.save()!);
    return filePath;
  }

  Future<String> _exportToPdf() async {
    final directory = await getApplicationDocumentsDirectory();

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${directory.path}/cattle_records_$timestamp.pdf';
    final pdf = pw.Document();

    // Prepare data before creating PDF
    final recordsToExport =
        _selectedCattle.isEmpty ? filteredCattle : await _getSelectedCattle();

    // Convert records to PDF-friendly format
    final tableData = recordsToExport.map((cattle) {
      final displayData = _getResolvedCattleDisplayData(cattle);

      return [
        displayData['name'],
        displayData['tagId'],
        displayData['gender'],
        displayData['animalTypeName'],
        displayData['dateOfBirth']?.toString() ?? '',
        displayData['breedName'],
      ];
    }).toList();

    pdf.addPage(
      pw.Page(
        build: (context) {
          return pw.Column(
            children: [
              pw.Header(
                level: 0,
                child: pw.Text('Cattle Records',
                    style: pw.TextStyle(
                        fontSize: 24, fontWeight: pw.FontWeight.bold)),
              ),
              pw.SizedBox(height: 20),
              pw.TableHelper.fromTextArray(
                headers: [
                  'Name',
                  'Tag ID',
                  'Gender',
                  'Animal Type',
                  'Date of Birth',
                  'Breed'
                ],
                data: tableData,
                headerStyle: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                headerDecoration: const pw.BoxDecoration(
                  color: PdfColors.grey300,
                ),
                cellHeight: 30,
                cellAlignments: {
                  0: pw.Alignment.centerLeft,
                  1: pw.Alignment.centerLeft,
                  2: pw.Alignment.centerLeft,
                  3: pw.Alignment.centerLeft,
                  4: pw.Alignment.centerLeft,
                  5: pw.Alignment.centerLeft,
                },
              ),
            ],
          );
        },
      ),
    );

    final pdfFile = File(filePath);
    await pdfFile.writeAsBytes(await pdf.save());
    return filePath;
  }

  String _escapeCsvField(String field) {
    if (field.contains(',') || field.contains('"') || field.contains('\n')) {
      return '"${field.replaceAll('"', '""')}"';
    }
    return field;
  }

  // Add this helper method to determine text color based on background color

  // Replace the stylish badge method with a simpler non-gradient version
  Widget _buildBadge(
      {required String label,
      required Color backgroundColor,
      Color? textColor,
      IconData? icon}) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, size: 14, color: textColor ?? Colors.white),
            const SizedBox(width: 4),
          ],
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                color: textColor ?? Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get the first letter of a name
  String _getInitial(String? name) {
    if (name == null || name.isEmpty) return '?';
    return name.trim()[0].toUpperCase();
  }

  Widget _buildFilterBar() {
    ThemeData theme = Theme.of(context);
    const borderRadius = BorderRadius.all(Radius.circular(8.0));

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Full-width Search Bar with border
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: _Strings.searchCattle,
              prefixIcon: const Icon(Icons.search, size: 20),
              isDense: true,
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              border: const OutlineInputBorder(borderRadius: borderRadius),
              enabledBorder: OutlineInputBorder(
                borderRadius: borderRadius,
                borderSide:
                    BorderSide(color: theme.colorScheme.outline.withAlpha(128)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: borderRadius,
                borderSide: BorderSide(color: theme.colorScheme.primary),
              ),
              filled: true,
              fillColor: theme.scaffoldBackgroundColor.withAlpha(204),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear, size: 18),
                      onPressed: () {
                        _searchController.clear();
                        setState(() => _searchQuery = '');
                      },
                    )
                  : null,
            ),
          ),

          // Sort Row - evenly spaced
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Row(
              children: [
                // Sort Direction Button
                Container(
                  width: 48,
                  height: 48,
                  margin: const EdgeInsets.only(right: 8),
                  child: IconButton(
                    icon: Icon(
                      _sortAscending
                          ? Icons.arrow_upward
                          : Icons.arrow_downward,
                      size: 20,
                    ),
                    onPressed: () {
                      setState(() {
                        _sortAscending = !_sortAscending;
                      });
                    },
                    tooltip:
                        _sortAscending ? 'Sort Ascending' : 'Sort Descending',
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.primaryContainer,
                      foregroundColor: theme.colorScheme.onPrimaryContainer,
                      shape: const RoundedRectangleBorder(
                          borderRadius: borderRadius),
                    ),
                  ),
                ),
                // Sort Field Dropdown
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _sortBy,
                    style: TextStyle(
                      color: theme.colorScheme.onSurface,
                    ),
                    decoration: InputDecoration(
                      labelText: 'Sort By',
                      labelStyle: TextStyle(
                        color: theme.colorScheme.onSurface,
                      ),
                      border:
                          const OutlineInputBorder(borderRadius: borderRadius),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: borderRadius,
                        borderSide: BorderSide(
                            color: theme.colorScheme.outline.withAlpha(128)),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: borderRadius,
                        borderSide:
                            BorderSide(color: theme.colorScheme.primary),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      isDense: true,
                    ),
                    items: [
                      DropdownMenuItem(
                          value: 'Name',
                          child: Text('Name',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                            ))),
                      DropdownMenuItem(
                          value: 'Tag',
                          child: Text('Tag ID',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                            ))),
                      DropdownMenuItem(
                          value: 'dateOfBirth',
                          child: Text('Date of Birth',
                            style: TextStyle(
                              color: theme.colorScheme.onSurface,
                            ))),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _sortBy = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Filter Row - all 3 filters in one row (using PopupMenuButton like breeding records)
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Row(
              children: [
                // Gender Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => ['All', 'Male', 'Female']
                          .map((value) => PopupMenuItem(
                                value: value,
                                child: Text(value),
                              ))
                          .toList(),
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedGender = value;
                          });
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedGender,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name ?? 'Unknown',
                              child: Text(type.name ?? 'Unknown'),
                            )),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedAnimalType = value;
                          });
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Breed Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Breeds'),
                        ),
                        ..._breeds.map((breed) => PopupMenuItem(
                              value: breed.name ?? 'Unknown',
                              child: Text(breed.name ?? 'Unknown'),
                            )),
                      ],
                      onSelected: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedBreed = value;
                          });
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedBreed,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Clear Filters Button (only shown when filters are active)
          if (_selectedGender != 'All' ||
              _selectedAnimalType != 'All' ||
              _selectedBreed != 'All')
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: ElevatedButton.icon(
                icon: const Icon(Icons.clear_all, size: 16),
                label: const Text('Clear All Filters'),
                onPressed: () {
                  setState(() {
                    _selectedGender = 'All';
                    _selectedAnimalType = 'All';
                    _selectedBreed = 'All';
                  });
                },
                style: ElevatedButton.styleFrom(
                  foregroundColor: theme.colorScheme.onError,
                  backgroundColor: theme.colorScheme.errorContainer,
                  shape:
                      const RoundedRectangleBorder(borderRadius: borderRadius),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  visualDensity: VisualDensity.compact,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    List<CattleIsar> displayedCattle = filteredCattle;

    return Scaffold(
      appBar: AppBarConfig.standard(
        title: _Strings.title,
        actions: [
          if (_isSelectionMode) ...[
            Tooltip(
              message: _Strings.selectAll,
              child: Checkbox(
                value: _selectedCattle.length == filteredCattle.length,
                tristate: _selectedCattle.isNotEmpty &&
                    _selectedCattle.length != filteredCattle.length,
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      for (var c in filteredCattle) {
                        if (c.businessId != null) {
                          _selectedCattle.add(c.businessId!);
                        }
                      }
                    } else {
                      _selectedCattle.clear();
                    }
                  });
                },
              ),
            ),
            Text(
              '${_selectedCattle.length} ${_Strings.selected}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 8),
          ],
          if (_isSelectionMode)
            Tooltip(
              message: _Strings.cancelSelection,
              child: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _isSelectionMode = false;
                    _selectedCattle.clear();
                  });
                },
              ),
            ),
          Tooltip(
            message: _Strings.exportRecords,
            child: IconButton(
              icon: const Icon(Icons.download),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text(_Strings.exportRecords),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        ListTile(
                          leading: const Icon(Icons.table_chart),
                          title: const Text('Excel'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('excel');
                          },
                        ),
                        ListTile(
                          leading: const Icon(Icons.picture_as_pdf),
                          title: const Text('PDF'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('pdf');
                          },
                        ),
                        ListTile(
                          leading: const Icon(Icons.insert_drive_file),
                          title: const Text('CSV'),
                          onTap: () {
                            Navigator.pop(context);
                            _exportRecords('csv');
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          Tooltip(
            message: _isSelectionMode
                ? _Strings.deleteSelected
                : _Strings.selectToDelete,
            child: IconButton(
              icon: _isSelectionMode
                  ? const Icon(Icons.delete_forever)
                  : const Icon(Icons.delete_outline),
              onPressed: () {
                if (_isSelectionMode) {
                  if (_selectedCattle.isNotEmpty) {
                    _showDeleteConfirmation(_selectedCattle.toList());
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text(_Strings.pleaseSelectItems),
                        backgroundColor:
                            Theme.of(context).colorScheme.errorContainer,
                      ),
                    );
                  }
                } else {
                  setState(() {
                    _isSelectionMode = true;
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 8), // Add some padding at the end
        ],
      ),
      floatingActionButton: FabStyles.add(
        onPressed: _showAddCattleDialog,
        tooltip: _Strings.addCattle,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildFilterBar(),
                Expanded(
                  child: ListView.builder(
                    itemCount: displayedCattle.length,
                    itemBuilder: (context, index) {
                      final cattle = displayedCattle[index];
                      final isSelected =
                          _selectedCattle.contains(cattle.businessId);

                      // Get resolved display data
                      final displayData = _getResolvedCattleDisplayData(cattle);

                      return Card(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            if (constraints.maxWidth > 600) {
                              // Wide screen layout - enhanced row with better visuals
                              return InkWell(
                                onTap: () {
                                  if (cattle.businessId != null) {
                                    _navigateToCattleDetail(
                                      businessId: cattle.businessId!,
                                      existingCattle: cattle,
                                      onCattleUpdated: (updatedCattle) {},
                                    );
                                  }
                                },
                                onLongPress: () {
                                  if (!_isSelectionMode &&
                                      cattle.businessId != null) {
                                    setState(() {
                                      _isSelectionMode = true;
                                      _selectedCattle.add(cattle.businessId!);
                                    });
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4.0, horizontal: 8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      // Selection checkbox or circle avatar with initial
                                      _isSelectionMode
                                          ? Checkbox(
                                              value: isSelected,
                                              onChanged: (bool? value) {
                                                setState(() {
                                                  if (value == true &&
                                                      cattle.businessId !=
                                                          null) {
                                                    _selectedCattle.add(
                                                        cattle.businessId!);
                                                  } else if (cattle
                                                          .businessId !=
                                                      null) {
                                                    _selectedCattle.remove(
                                                        cattle.businessId!);
                                                  }
                                                });
                                              },
                                            )
                                          : CircleAvatar(
                                              radius: 24,
                                              backgroundColor: Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                              child: Text(
                                                _getInitial(
                                                    displayData['name']),
                                                style: TextStyle(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .onPrimary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),

                                      const SizedBox(width: 16),

                                      // Name and Tag ID in one row
                                      Expanded(
                                        flex: 3,
                                        child: Row(
                                          children: [
                                            Text(
                                              '${displayData['name']} (${displayData['tagId'].toUpperCase()})',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                  ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Badge container with evenly spaced badges
                                      Expanded(
                                        flex:
                                            6, // Larger flex to give more space to badges
                                        child: Row(
                                          children: [
                                            // Animal Type badge
                                            Expanded(
                                              flex: 2,
                                              child: Center(
                                                child: _buildBadge(
                                                  label: displayData[
                                                      'animalTypeName'],
                                                  backgroundColor:
                                                      Colors.purple,
                                                  textColor: Theme.of(context)
                                                      .colorScheme
                                                      .onPrimary,
                                                ),
                                              ),
                                            ),

                                            // Breed badge
                                            Expanded(
                                              flex: 2,
                                              child: Center(
                                                child: _buildBadge(
                                                  label:
                                                      displayData['breedName'],
                                                  backgroundColor: Colors.green,
                                                  textColor: Theme.of(context)
                                                      .colorScheme
                                                      .onSecondary,
                                                ),
                                              ),
                                            ),

                                            // Gender badge
                                            Expanded(
                                              flex: 2,
                                              child: Center(
                                                child: _buildBadge(
                                                  label: displayData['gender'],
                                                  backgroundColor: displayData[
                                                      'genderColor'],
                                                  icon:
                                                      displayData['genderIcon'],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      const SizedBox(width: 8),

                                      // Actions
                                      PopupMenuButton<String>(
                                        onSelected: (String choice) {
                                          if (choice == 'Edit') {
                                            _showEditCattleDialog(cattle);
                                          } else if (choice == 'Delete') {
                                            if (cattle.businessId != null) {
                                              _showDeleteConfirmation(
                                                  [cattle.businessId!]);
                                            }
                                          } else if (choice == 'View') {
                                            if (cattle.businessId != null) {
                                              _navigateToCattleDetail(
                                                businessId: cattle.businessId!,
                                                existingCattle: cattle,
                                                onCattleUpdated:
                                                    (updatedCattle) {},
                                              );
                                            }
                                          }
                                        },
                                        itemBuilder: (BuildContext context) => [
                                          const PopupMenuItem(
                                            value: 'View',
                                            child: Text(_Strings.viewDetails),
                                          ),
                                          const PopupMenuItem(
                                            value: 'Edit',
                                            child: Text(_Strings.edit),
                                          ),
                                          const PopupMenuItem(
                                            value: 'Delete',
                                            child: Text(_Strings.delete),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            } else {
                              // Narrow screen layout with updated styling
                              return InkWell(
                                onTap: () {
                                  if (cattle.businessId != null) {
                                    _navigateToCattleDetail(
                                      businessId: cattle.businessId!,
                                      existingCattle: cattle,
                                      onCattleUpdated: (updatedCattle) {},
                                    );
                                  }
                                },
                                onLongPress: () {
                                  if (!_isSelectionMode &&
                                      cattle.businessId != null) {
                                    setState(() {
                                      _isSelectionMode = true;
                                      _selectedCattle.add(cattle.businessId!);
                                    });
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      // Leading element: Selection checkbox or circle avatar with initial
                                      _isSelectionMode
                                          ? Checkbox(
                                              value: isSelected,
                                              onChanged: (bool? value) {
                                                setState(() {
                                                  if (value == true &&
                                                      cattle.businessId !=
                                                          null) {
                                                    _selectedCattle.add(
                                                        cattle.businessId!);
                                                  } else if (cattle
                                                          .businessId !=
                                                      null) {
                                                    _selectedCattle.remove(
                                                        cattle.businessId!);
                                                  }
                                                });
                                              },
                                            )
                                          : CircleAvatar(
                                              radius: 24,
                                              backgroundColor: Theme.of(context)
                                                  .colorScheme
                                                  .primary,
                                              child: Text(
                                                _getInitial(
                                                    displayData['name']),
                                                style: TextStyle(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .onPrimary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ),

                                      // Spacing
                                      const SizedBox(width: 16),

                                      // Content area
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            // Name and Tag ID in one row
                                            Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    '${displayData['name']} (${displayData['tagId'].toUpperCase()})',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .titleMedium
                                                        ?.copyWith(
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                              ],
                                            ),

                                            // Add spacing between elements
                                            const SizedBox(height: 12),

                                            // Badges in a single row
                                            Row(
                                              children: [
                                                // Animal Type badge
                                                Expanded(
                                                  flex: 2,
                                                  child: Center(
                                                    child: _buildBadge(
                                                      label: displayData[
                                                          'animalTypeName'],
                                                      backgroundColor:
                                                          Colors.blue.shade300,
                                                      textColor:
                                                          Theme.of(context)
                                                              .colorScheme
                                                              .onPrimary,
                                                    ),
                                                  ),
                                                ),

                                                // Breed badge
                                                Expanded(
                                                  flex: 2,
                                                  child: Center(
                                                    child: _buildBadge(
                                                      label: displayData[
                                                          'breedName'],
                                                      backgroundColor:
                                                          Colors.green.shade300,
                                                      textColor:
                                                          Theme.of(context)
                                                              .colorScheme
                                                              .onSecondary,
                                                    ),
                                                  ),
                                                ),

                                                // Gender badge
                                                Expanded(
                                                  flex: 2,
                                                  child: Center(
                                                    child: _buildBadge(
                                                      label:
                                                          displayData['gender'],
                                                      backgroundColor:
                                                          displayData[
                                                              'genderColor'],
                                                      icon: displayData[
                                                          'genderIcon'],
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Action menu
                                      PopupMenuButton<String>(
                                        onSelected: (String choice) {
                                          if (choice == 'Edit') {
                                            _showEditCattleDialog(cattle);
                                          } else if (choice == 'Delete') {
                                            if (cattle.businessId != null) {
                                              _showDeleteConfirmation(
                                                  [cattle.businessId!]);
                                            }
                                          } else if (choice == 'View') {
                                            if (cattle.businessId != null) {
                                              _navigateToCattleDetail(
                                                businessId: cattle.businessId!,
                                                existingCattle: cattle,
                                                onCattleUpdated:
                                                    (updatedCattle) {},
                                              );
                                            }
                                          }
                                        },
                                        itemBuilder: (BuildContext context) => [
                                          const PopupMenuItem(
                                            value: 'View',
                                            child: Text(_Strings.viewDetails),
                                          ),
                                          const PopupMenuItem(
                                            value: 'Edit',
                                            child: Text(_Strings.edit),
                                          ),
                                          const PopupMenuItem(
                                            value: 'Delete',
                                            child: Text(_Strings.delete),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
    );
  }

  void _showDeleteConfirmation(List<String> cattleIds) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final bool deleteAll = cattleIds.isEmpty;
        final int count = deleteAll ? filteredCattle.length : cattleIds.length;
        final String itemsDescription = deleteAll ? "filtered" : "selected";

        return AlertDialog(
          title:
              Text('Delete ${deleteAll ? 'All Filtered' : 'Selected'} Records'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  'Are you sure you want to delete $count $itemsDescription cattle record${count > 1 ? 's' : ''}?'),
              const SizedBox(height: 16),
              const Text(
                'Warning: This action cannot be undone.',
                style: TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('DELETE'),
            ),
          ],
        );
      },
    ).then((confirmed) async {
      if (confirmed == true) {
        // Show a loading indicator while deletion is in progress
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 24,
                  height: 24,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                ),
                SizedBox(width: 16),
                Text('Deleting cattle...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );

        try {
          if (cattleIds.isEmpty) {
            // Delete all filtered cattle
            final cattleToDelete = filteredCattle;
            debugPrint(
                'DEBUG: Preparing to delete ${cattleToDelete.length} filtered cattle records');

            int successCount = 0;
            List<String> failedIds = [];

            for (var cattle in cattleToDelete) {
              if (cattle.businessId != null) {
                try {
                  await _cattleHandler.deleteCattle(cattle.businessId!);
                  successCount++;
                } catch (e) {
                  debugPrint(
                      'ERROR: Failed to delete cattle ${cattle.businessId}: $e');
                  failedIds.add(cattle.tagId ?? 'Unknown');
                }
              }
            }

            if (!mounted) return;

            if (failedIds.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Successfully deleted $successCount cattle records'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Deleted $successCount records. Failed to delete ${failedIds.length} records'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          } else {
            // Delete selected cattle
            debugPrint(
                'DEBUG: Preparing to delete ${cattleIds.length} selected cattle records');

            int successCount = 0;
            List<String> failedIds = [];

            for (var cattleId in cattleIds) {
              try {
                await _cattleHandler.deleteCattle(cattleId);
                successCount++;
              } catch (e) {
                debugPrint('ERROR: Failed to delete cattle $cattleId: $e');
                failedIds.add(cattleId);
                logger.e('Error deleting cattle with ID: $cattleId - $e');
              }
            }

            setState(() {
              _selectedCattle.clear();
              _isSelectionMode = false;
            });

            if (!mounted) return;

            if (failedIds.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Successfully deleted $successCount cattle records'),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Deleted $successCount records. Failed to delete ${failedIds.length} records'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }

          // Reload data after deletion
          await _loadData();
        } catch (e, s) {
          debugPrint('ERROR: Exception during cattle deletion: $e\n$s');

          if (!mounted) return;

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content:
                  Text('Error deleting cattle: ${_getReadableErrorMessage(e)}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );

          // Still reload data to keep UI in sync
          await _loadData();
        }
      }
    });
  }

  /// Helper method to get resolved cattle display data to avoid code duplication
  Map<String, dynamic> _getResolvedCattleDisplayData(CattleIsar cattle) {
    final result = <String, dynamic>{};

    // Add basic cattle data
    result['name'] = cattle.name ?? _Strings.noName;
    result['tagId'] = cattle.tagId ?? '';
    result['gender'] = cattle.gender ?? _Strings.unknown;
    result['dateOfBirth'] = cattle.dateOfBirth;

    // Resolve breed with fallback
    final breedId = cattle.breedId;
    final breed = _breedMap[breedId] ??
        (breedId != null
            ? (() {
                // Log the issue for debugging
                debugPrint(
                    '⚠️ Fallback: No breed with ID [$breedId] found for cattle ${cattle.name}. Using default.');
                return BreedCategoryIsar()..name = _Strings.unknownBreed;
              })()
            : BreedCategoryIsar()
          ..name = _Strings.unknownBreed);

    result['breed'] = breed;
    result['breedName'] = breed.name ?? _Strings.unknownBreed;

    // Resolve animal type with fallback
    final animalTypeId = cattle.animalTypeId;
    final animalType = _animalTypeMap[animalTypeId] ??
        (animalTypeId != null
            ? (() {
                // Log the issue for debugging
                debugPrint(
                    '⚠️ Fallback: No animal type with ID [$animalTypeId] found for cattle ${cattle.name}. Using default.');
                return AnimalTypeIsar()..name = _Strings.unknownType;
              })()
            : AnimalTypeIsar()
          ..name = _Strings.unknownType);

    result['animalType'] = animalType;
    result['animalTypeName'] = animalType.name ?? _Strings.unknownType;

    // Determine gender-specific display elements
    result['genderColor'] = (cattle.gender?.toLowerCase() ?? '') == 'male'
        ? Colors.blue
        : Colors.pink;

    result['genderIcon'] = (cattle.gender?.toLowerCase() ?? '') == 'male'
        ? Icons.male
        : Icons.female;

    return result;
  }
}
